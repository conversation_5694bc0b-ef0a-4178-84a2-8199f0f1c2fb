{"mcpServers": {"supabase": {"type": "stdio", "command": "node", "args": ["/mnt/c/mcp_servers/selfhosted-supabase-mcp/dist/index.js"], "env": {"SUPABASE_URL": "https://devdb.syncrobit.net", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY", "SUPABASE_SERVICE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDdO6gF5a6XOvnmlqRLa8whV", "JWT_SECRET": "d124QZSveM2X1rAdcDdO6gF5a6XOvnmlqRLa8whV", "DATABASE_URL": "postgresql://postgres:<EMAIL>:5432/postgres"}}, "taskmaster-ai": {"type": "stdio", "command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"]}, "playwright": {"type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest"]}}}