import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Enhanced auth operations with error handling
export const authOperations = {
  async signUp(email: string, password: string, metadata?: Record<string, any>) {
    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata
        }
      })

      if (error) {
        console.error('Sign up error:', error)
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Sign up failed:', error)
      throw error
    }
  },

  async signIn(email: string, password: string) {
    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Sign in error:', error)
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Sign in failed:', error)
      throw error
    }
  },

  async signOut() {
    try {
      const supabase = createClient()
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Sign out error:', error)
        throw new Error(error.message)
      }
    } catch (error) {
      console.error('Sign out failed:', error)
      throw error
    }
  },

  async getSession() {
    try {
      const supabase = createClient()
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        console.error('Get session error:', error)
        return null
      }

      return session
    } catch (error) {
      console.error('Get session failed:', error)
      return null
    }
  },

  async refreshSession() {
    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        console.error('Refresh session error:', error)
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Refresh session failed:', error)
      throw error
    }
  }
}

// Type exports for better TypeScript support
export type { User, Session, AuthError } from '@supabase/supabase-js'