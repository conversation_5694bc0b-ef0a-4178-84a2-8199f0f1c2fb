import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';

async function testValidateLogin() {
  try {
    const response = await fetch('http://localhost:8765/auth/validate-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Ahayh@5096$'
      })
    });

    const data = await response.json();
    console.log('Validate Login Response:', response.status, data);
  } catch (error) {
    console.error('Validate Login Error:', error);
  }
}

async function testSupabaseAuth() {
  try {
    const supabaseUrl = 'https://devdb.syncrobit.net';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';

    const supabase = createClient(supabaseUrl, supabaseKey);

    console.log('Testing Supabase authentication...');
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Ahayh@5096$'
    });

    if (error) {
      console.error('Supabase Auth Error:', error);
    } else {
      console.log('Supabase Auth Success:', data);
    }
  } catch (error) {
    console.error('Supabase Test Error:', error);
  }
}

async function main() {
  console.log('=== Testing Authentication Flow ===');
  
  console.log('\n1. Testing validate-login endpoint...');
  await testValidateLogin();
  
  console.log('\n2. Testing direct Supabase authentication...');
  await testSupabaseAuth();
}

// Create package.json for ES modules
import { writeFileSync } from 'fs';
try {
  writeFileSync('package.json', JSON.stringify({ type: 'module' }, null, 2));
} catch (e) {
  // Ignore if already exists
}

main();
